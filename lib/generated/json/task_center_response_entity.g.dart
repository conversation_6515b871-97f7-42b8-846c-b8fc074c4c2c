import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';

TaskCenterResponseEntity $TaskCenterResponseEntityFromJson(Map<String, dynamic> json) {
  final TaskCenterResponseEntity taskCenterResponseEntity = TaskCenterResponseEntity();
  final List<TaskEntity>? daily = (json['daily'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TaskEntity>(e) as TaskEntity).toList();
  if (daily != null) {
    taskCenterResponseEntity.daily = daily;
  }
  final List<TaskEntity>? invite = (json['invite'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TaskEntity>(e) as TaskEntity).toList();
  if (invite != null) {
    taskCenterResponseEntity.invite = invite;
  }
  final List<TaskEntity>? newUser = (json['newUser'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TaskEntity>(e) as TaskEntity).toList();
  if (newUser != null) {
    taskCenterResponseEntity.newUser = newUser;
  }
  final List<TaskEntity>? trade = (json['trade'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TaskEntity>(e) as TaskEntity).toList();
  if (trade != null) {
    taskCenterResponseEntity.trade = trade;
  }
  final String? signInRules = jsonConvert.convert<String>(json['signInRules']);
  if (signInRules != null) {
    taskCenterResponseEntity.signInRules = signInRules;
  }
  return taskCenterResponseEntity;
}

Map<String, dynamic> $TaskCenterResponseEntityToJson(TaskCenterResponseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['daily'] = entity.daily.map((v) => v.toJson()).toList();
  data['invite'] = entity.invite.map((v) => v.toJson()).toList();
  data['newUser'] = entity.newUser.map((v) => v.toJson()).toList();
  data['trade'] = entity.trade.map((v) => v.toJson()).toList();
  data['signInRules'] = entity.signInRules;
  return data;
}

extension TaskCenterResponseEntityExtension on TaskCenterResponseEntity {
  TaskCenterResponseEntity copyWith({
    List<TaskEntity>? daily,
    List<TaskEntity>? invite,
    List<TaskEntity>? newUser,
    List<TaskEntity>? trade,
    String? signInRules,
  }) {
    return TaskCenterResponseEntity()
      ..daily = daily ?? this.daily
      ..invite = invite ?? this.invite
      ..newUser = newUser ?? this.newUser
      ..trade = trade ?? this.trade
      ..signInRules = signInRules ?? this.signInRules;
  }
}

TaskEntity $TaskEntityFromJson(Map<String, dynamic> json) {
  final TaskEntity taskEntity = TaskEntity();
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    taskEntity.amount = amount;
  }
  final int? amountType = jsonConvert.convert<int>(json['amountType']);
  if (amountType != null) {
    taskEntity.amountType = amountType;
  }
  final int? availableNum = jsonConvert.convert<int>(json['availableNum']);
  if (availableNum != null) {
    taskEntity.availableNum = availableNum;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    taskEntity.content = content;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    taskEntity.endTime = endTime;
  }
  final int? expireType = jsonConvert.convert<int>(json['expireType']);
  if (expireType != null) {
    taskEntity.expireType = expireType;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    taskEntity.icon = icon;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    taskEntity.id = id;
  }
  final bool? isCompleted = jsonConvert.convert<bool>(json['isCompleted']);
  if (isCompleted != null) {
    taskEntity.isCompleted = isCompleted;
  }
  final bool? isReceived = jsonConvert.convert<bool>(json['isReceived']);
  if (isReceived != null) {
    taskEntity.isReceived = isReceived;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    taskEntity.name = name;
  }
  final String? rule = jsonConvert.convert<String>(json['rule']);
  if (rule != null) {
    taskEntity.rule = rule;
  }
  final String? startTime = jsonConvert.convert<String>(json['startTime']);
  if (startTime != null) {
    taskEntity.startTime = startTime;
  }
  final bool? status = jsonConvert.convert<bool>(json['status']);
  if (status != null) {
    taskEntity.status = status;
  }
  final int? taskType = jsonConvert.convert<int>(json['taskType']);
  if (taskType != null) {
    taskEntity.taskType = taskType;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    taskEntity.type = type;
  }
  return taskEntity;
}

Map<String, dynamic> $TaskEntityToJson(TaskEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amount'] = entity.amount;
  data['amountType'] = entity.amountType;
  data['availableNum'] = entity.availableNum;
  data['content'] = entity.content;
  data['endTime'] = entity.endTime;
  data['expireType'] = entity.expireType;
  data['icon'] = entity.icon;
  data['id'] = entity.id;
  data['isCompleted'] = entity.isCompleted;
  data['isReceived'] = entity.isReceived;
  data['name'] = entity.name;
  data['rule'] = entity.rule;
  data['startTime'] = entity.startTime;
  data['status'] = entity.status;
  data['taskType'] = entity.taskType;
  data['type'] = entity.type;
  return data;
}

extension TaskEntityExtension on TaskEntity {
  TaskEntity copyWith({
    double? amount,
    int? amountType,
    int? availableNum,
    String? content,
    String? endTime,
    int? expireType,
    String? icon,
    int? id,
    bool? isCompleted,
    bool? isReceived,
    String? name,
    String? rule,
    String? startTime,
    bool? status,
    int? taskType,
    int? type,
  }) {
    return TaskEntity()
      ..amount = amount ?? this.amount
      ..amountType = amountType ?? this.amountType
      ..availableNum = availableNum ?? this.availableNum
      ..content = content ?? this.content
      ..endTime = endTime ?? this.endTime
      ..expireType = expireType ?? this.expireType
      ..icon = icon ?? this.icon
      ..id = id ?? this.id
      ..isCompleted = isCompleted ?? this.isCompleted
      ..isReceived = isReceived ?? this.isReceived
      ..name = name ?? this.name
      ..rule = rule ?? this.rule
      ..startTime = startTime ?? this.startTime
      ..status = status ?? this.status
      ..taskType = taskType ?? this.taskType
      ..type = type ?? this.type;
  }
}