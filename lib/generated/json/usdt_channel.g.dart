import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';

USDTChannelListEntity $USDTChannelListEntityFromJson(Map<String, dynamic> json) {
  final USDTChannelListEntity uSDTChannelListEntity = USDTChannelListEntity();
  final List<USDTChannel>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<USDTChannel>(e) as USDTChannel).toList();
  if (list != null) {
    uSDTChannelListEntity.list = list;
  }
  return uSDTChannelListEntity;
}

Map<String, dynamic> $USDTChannelListEntityToJson(USDTChannelListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension USDTChannelListEntityExtension on USDTChannelListEntity {
  USDTChannelListEntity copyWith({
    List<USDTChannel>? list,
  }) {
    return USDTChannelListEntity()
      ..list = list ?? this.list;
  }
}

USDTChannel $USDTChannelFromJson(Map<String, dynamic> json) {
  final USDTChannel uSDTChannel = USDTChannel();
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    uSDTChannel.currency = currency;
  }
  final double? exchangeRate = jsonConvert.convert<double>(json['exchangeRate']);
  if (exchangeRate != null) {
    uSDTChannel.exchangeRate = exchangeRate;
  }
  final int? giveGiftType = jsonConvert.convert<int>(json['giveGiftType']);
  if (giveGiftType != null) {
    uSDTChannel.giveGiftType = giveGiftType;
  }
  final double? giveRate = jsonConvert.convert<double>(json['giveRate']);
  if (giveRate != null) {
    uSDTChannel.giveRate = giveRate;
  }
  final int? giveRuleType = jsonConvert.convert<int>(json['giveRuleType']);
  if (giveRuleType != null) {
    uSDTChannel.giveRuleType = giveRuleType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    uSDTChannel.id = id;
  }
  final double? maxAmount = jsonConvert.convert<double>(json['maxAmount']);
  if (maxAmount != null) {
    uSDTChannel.maxAmount = maxAmount;
  }
  final double? minAmount = jsonConvert.convert<double>(json['minAmount']);
  if (minAmount != null) {
    uSDTChannel.minAmount = minAmount;
  }
  final int? network = jsonConvert.convert<int>(json['network']);
  if (network != null) {
    uSDTChannel.network = network;
  }
  final String? rechargeAddress = jsonConvert.convert<String>(json['rechargeAddress']);
  if (rechargeAddress != null) {
    uSDTChannel.rechargeAddress = rechargeAddress;
  }
  final String? rechargeAmountOptions = jsonConvert.convert<String>(json['rechargeAmountOptions']);
  if (rechargeAmountOptions != null) {
    uSDTChannel.rechargeAmountOptions = rechargeAmountOptions;
  }
  final List<String>? rechargeAmountOptionsList = (json['rechargeAmountOptionsList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (rechargeAmountOptionsList != null) {
    uSDTChannel.rechargeAmountOptionsList = rechargeAmountOptionsList;
  }
  return uSDTChannel;
}

Map<String, dynamic> $USDTChannelToJson(USDTChannel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currency'] = entity.currency;
  data['exchangeRate'] = entity.exchangeRate;
  data['giveGiftType'] = entity.giveGiftType;
  data['giveRate'] = entity.giveRate;
  data['giveRuleType'] = entity.giveRuleType;
  data['id'] = entity.id;
  data['maxAmount'] = entity.maxAmount;
  data['minAmount'] = entity.minAmount;
  data['network'] = entity.network;
  data['rechargeAddress'] = entity.rechargeAddress;
  data['rechargeAmountOptions'] = entity.rechargeAmountOptions;
  data['rechargeAmountOptionsList'] = entity.rechargeAmountOptionsList;
  return data;
}

extension USDTChannelExtension on USDTChannel {
  USDTChannel copyWith({
    String? currency,
    double? exchangeRate,
    int? giveGiftType,
    double? giveRate,
    int? giveRuleType,
    int? id,
    double? maxAmount,
    double? minAmount,
    int? network,
    String? rechargeAddress,
    String? rechargeAmountOptions,
    List<String>? rechargeAmountOptionsList,
  }) {
    return USDTChannel()
      ..currency = currency ?? this.currency
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..giveGiftType = giveGiftType ?? this.giveGiftType
      ..giveRate = giveRate ?? this.giveRate
      ..giveRuleType = giveRuleType ?? this.giveRuleType
      ..id = id ?? this.id
      ..maxAmount = maxAmount ?? this.maxAmount
      ..minAmount = minAmount ?? this.minAmount
      ..network = network ?? this.network
      ..rechargeAddress = rechargeAddress ?? this.rechargeAddress
      ..rechargeAmountOptions = rechargeAmountOptions ?? this.rechargeAmountOptions
      ..rechargeAmountOptionsList = rechargeAmountOptionsList ?? this.rechargeAmountOptionsList;
  }
}