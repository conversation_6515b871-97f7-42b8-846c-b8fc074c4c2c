import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

USDTWalletList $USDTWalletListFromJson(Map<String, dynamic> json) {
  final USDTWalletList uSDTWalletList = USDTWalletList();
  final List<USDTWallet>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<USDTWallet>(e) as USDTWallet).toList();
  if (list != null) {
    uSDTWalletList.list = list;
  }
  return uSDTWalletList;
}

Map<String, dynamic> $USDTWalletListToJson(USDTWalletList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension USDTWalletListExtension on USDTWalletList {
  USDTWalletList copyWith({
    List<USDTWallet>? list,
  }) {
    return USDTWalletList()
      ..list = list ?? this.list;
  }
}

USDTWallet $USDTWalletFromJson(Map<String, dynamic> json) {
  final USDTWallet uSDTWallet = USDTWallet();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    uSDTWallet.id = id;
  }
  final int? isWithdrawDefault = jsonConvert.convert<int>(json['isWithdrawDefault']);
  if (isWithdrawDefault != null) {
    uSDTWallet.isWithdrawDefault = isWithdrawDefault;
  }
  final int? network = jsonConvert.convert<int>(json['network']);
  if (network != null) {
    uSDTWallet.network = network;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    uSDTWallet.userId = userId;
  }
  final String? walletAddress = jsonConvert.convert<String>(json['walletAddress']);
  if (walletAddress != null) {
    uSDTWallet.walletAddress = walletAddress;
  }
  return uSDTWallet;
}

Map<String, dynamic> $USDTWalletToJson(USDTWallet entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['isWithdrawDefault'] = entity.isWithdrawDefault;
  data['network'] = entity.network;
  data['userId'] = entity.userId;
  data['walletAddress'] = entity.walletAddress;
  return data;
}

extension USDTWalletExtension on USDTWallet {
  USDTWallet copyWith({
    int? id,
    int? isWithdrawDefault,
    int? network,
    int? userId,
    String? walletAddress,
  }) {
    return USDTWallet()
      ..id = id ?? this.id
      ..isWithdrawDefault = isWithdrawDefault ?? this.isWithdrawDefault
      ..network = network ?? this.network
      ..userId = userId ?? this.userId
      ..walletAddress = walletAddress ?? this.walletAddress;
  }
}