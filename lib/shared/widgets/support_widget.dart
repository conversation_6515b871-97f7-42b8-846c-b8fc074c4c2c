import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/system_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:url_launcher/url_launcher.dart';

class SupportWidget extends StatelessWidget {
  const SupportWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: '${'encounterAnyProblem'.tr()} ',
          style: context.textTheme.tertiary,
          children: [
            TextSpan(
              text: 'online_support'.tr(),
              style: context.textTheme.primary.copyWith(
                decoration: TextDecoration.underline,
                decorationColor: context.colorTheme.textPrimary,
                decorationThickness: 1,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () => SystemUtil.contactService(context),
            ),
          ],
        ),
      ),
    );
  }
}
