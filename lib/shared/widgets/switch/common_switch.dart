import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class CommonSwitch extends StatelessWidget {
  final bool value;
  final Alignment? alignment;
  final Color? activeColor;
  final ValueChanged<bool> onChanged;

  const CommonSwitch({
    super.key,
    required this.value,
    this.alignment,
    this.activeColor,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return FlutterSwitch(
      height: 22.gw,
      width: 44.gw,
      padding: 1,
      toggleSize: 20.gw,
      value: value,
      onToggle: onChanged,
      activeColor: activeColor ?? context.theme.primaryColor,
    );
  }
}
