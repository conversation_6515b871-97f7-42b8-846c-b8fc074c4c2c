

import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 存款
class DepositApi {

  /// 获取所有USDT充值渠道
  static Future<List<USDTChannel>> fetchUsdtList() async {
    final res = await Http().request<USDTChannelListEntity>(
      ApiEndpoints.getDepositUsdtList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }


}