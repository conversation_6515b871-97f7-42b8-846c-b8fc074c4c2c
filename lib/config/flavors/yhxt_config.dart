import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';

void setupYHXTConfig() {
  AppConfig(
    flavor: Flavor.yhxt,
    appName: '沅和信投',
    siteId: "81",
    environment: "prod",
    skinStyle: AppSkinStyle.kGP,
    colorSchemeStyle: ColorSchemeStyle.kDefault,
    baseUrl: 'https://629328.com',
    marketWsUrl: 'wss://629328.com',
    inviteLinkUrl: 'https://629328.com/#/?inviteCode=',
    currentTradingModel: TradingMode.stockAndFutures,
    // AES encryption key for RSYP flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaLoginKey: '7650f145f0824ba6973d99d43a99d15c',
    wangYiCaptchaSMSKey: '0ecb64a2b8cc46d2b53c6e42d08ad708',
    pushAppKey: '689a76e7db852a0043f8cf5e',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/prod/81/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/prod/81/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/prod/81/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/prod/81/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/prod/81/app_api.json",
    ],
  );

  // AppConfig(
  //   flavor: Flavor.yhxt,
  //   appName: '沅和信投',
  //   siteId: "81",
  //   environment: "prod",
  //   skinStyle: AppSkinStyle.kGP,
  //   colorSchemeStyle: ColorSchemeStyle.kDefault,
  //   baseUrl: 'https://abcdefg.gphome.club',
  //   marketWsUrl: 'wss://abcdefg.gphome.club',
  //   inviteLinkUrl: 'https://abcdefg.gphome.club/#/?inviteCode=',
  //   currentTradingModel: TradingMode.stockAndFutures,
  //   // AES encryption key for GP flavor
  //   encryptionKey: '8JUOEEGjDsmrl30P',
  //   wangYiCaptchaLoginKey: '7650f145f0824ba6973d99d43a99d15c',
  //   wangYiCaptchaSMSKey: '0ecb64a2b8cc46d2b53c6e42d08ad708',
  //   pushAppKey: '689a76e7db852a0043f8cf5e',
  //   ossUrls: [
  //     "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/pre/1/app_api.json",
  //     "https://bj-1337543130.cos.ap-beijing.myqcloud.com/pre/1/app_api.json",
  //     "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/pre/1/app_api.json",
  //     "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/pre/1/app_api.json",
  //     "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/pre/1/app_api.json",
  //   ],
  // );
}
