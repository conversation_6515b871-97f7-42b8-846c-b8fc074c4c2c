import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'usdt_deposit_in_process_cubit.dart';
import 'usdt_deposit_in_process_state.dart';

class UsdtDepositInProcessPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UsdtDepositInProcessCubit, UsdtDepositInProcessState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: context.theme.scaffoldBackgroundColor,
          appBar: AppBar(
            backgroundColor: context.theme.cardColor,
            surfaceTintColor: Colors.transparent,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: context.colorTheme.textPrimary),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(
              'usdtRecharge'.tr(), // USDT充值
              style: context.textTheme.title.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
            centerTitle: true,
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                // 顶部渐变背景
                Container(
                  height: 100.gh,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        context.theme.primaryColor.withValues(alpha: 0.1),
                        context.theme.scaffoldBackgroundColor,
                      ],
                    ),
                  ),
                ),

                // 订单详情卡片
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: ShadowBox(
                    borderRadius: BorderRadius.circular(12.gr),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 0,
                      children: [
                        _buildOrderInfoRow(
                            context, 'orderNumber'.tr(), state.orderNumber ?? 'GSR1740121477402PHLJU', true), // 订单号
                        _buildOrderInfoRow(
                            context, 'rechargeAmount'.tr(), state.rechargeAmount ?? '¥100.00', true), // 充值金额
                        _buildOrderInfoRow(
                            context, 'depositQuantity'.tr(), state.depositQuantity ?? '13.96', true), // 充币数量
                        _buildOrderInfoRow(context, 'exchangeRate'.tr(), state.exchangeRate ?? '1:7.16', false), // 汇率
                        _buildOrderInfoRow(context, 'depositAddress'.tr(),
                            state.depositAddress ?? '1C8XY3ZQZ3W2N9\nW8QQ8P9MYF', true), // 充币地址
                        _buildOrderInfoRow(
                            context, 'rechargeNetwork'.tr(), state.rechargeNetwork ?? 'TRC20', true), // 充值网络
                        _buildOrderInfoRow(
                            context,
                            'orderStatus'.tr(),
                            'waitingForPayment'
                                .tr(args: [context.read<UsdtDepositInProcessCubit>().getFormattedCountdown()]),
                            false), // 订单状态
                      ],
                    ),
                  ),
                ),

                16.verticalSpace,

                // 重要提示
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: Text(
                    'pleaseCopyCompletelyAndVerifyCarefully'.tr(), // 请务必完整复制并仔细核对,以免造成损失
                    style: context.textTheme.regular.fs12.copyWith(
                      color: context.colorTheme.textRegular,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                24.verticalSpace,

                // 二维码区域
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: Column(
                    children: [
                      Text(
                        'depositQrCode'.tr(), // 充币二维码
                        style: context.textTheme.title.copyWith(
                          color: context.colorTheme.textPrimary,
                        ),
                      ),
                      16.verticalSpace,
                      Container(
                        padding: EdgeInsets.all(20.gw),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12.gr),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Container(
                          width: 200.gw,
                          height: 200.gh,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.gr),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.qr_code,
                              size: 180.gr,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                100.verticalSpace, // 为底部按钮留出空间
              ],
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(16.gw),
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CommonButton(
                  onPressed: () {
                    context.read<UsdtDepositInProcessCubit>().contactCustomerService();
                  },
                  title: 'rechargeCompletedContactCustomerService'.tr(), // 已完成充值,联系客服确认订单
                  enable: true,
                ),
                16.verticalSpace,
                SupportWidget(),
                16.verticalSpace,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOrderInfoRow(BuildContext context, String label, String value, bool showCopyIcon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.gh),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: context.textTheme.regular.fs13.copyWith(
                color: context.colorTheme.textRegular,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: context.textTheme.regular.fs13.copyWith(
                      color: context.colorTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (showCopyIcon) ...[
                  8.horizontalSpace,
                  GestureDetector(
                    onTap: () {
                      context.read<UsdtDepositInProcessCubit>().copyToClipboard(value);
                    },
                    child: Icon(
                      Icons.copy,
                      size: 18.gr,
                      color: context.theme.primaryColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
