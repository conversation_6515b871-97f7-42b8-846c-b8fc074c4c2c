import 'package:bloc/bloc.dart';
import 'dart:async';

import 'usdt_deposit_in_process_state.dart';

class UsdtDepositInProcessCubit extends Cubit<UsdtDepositInProcessState> {
  Timer? _countdownTimer;

  UsdtDepositInProcessCubit() : super(const UsdtDepositInProcessState()) {
    _initOrderData();
    _startCountdown();
  }

  void _initOrderData() {
    // 模拟订单数据，实际应该从API获取
    emit(state.copyWith(
      orderNumber: 'GSR1740121477402PHLJU',
      rechargeAmount: '¥100.00',
      depositQuantity: '13.96',
      exchangeRate: '1:7.16',
      depositAddress: '1C8XY3ZQZ3W2N9\nW8QQ8P9MYF',
      rechargeNetwork: 'TRC20',
      orderStatus: '等待付款',
      countdownSeconds: 300, // 5分钟倒计时
    ));
  }

  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.countdownSeconds != null && state.countdownSeconds! > 0) {
        emit(state.copyWith(countdownSeconds: state.countdownSeconds! - 1));
      } else {
        timer.cancel();
        // 倒计时结束，可以更新订单状态
        emit(state.copyWith(orderStatus: '支付超时'));
      }
    });
  }

  String getFormattedCountdown() {
    if (state.countdownSeconds == null) return '';
    final minutes = state.countdownSeconds! ~/ 60;
    final seconds = state.countdownSeconds! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void copyToClipboard(String text) {
    // TODO: 实现复制到剪贴板功能
    print('复制到剪贴板: $text');
  }

  void contactCustomerService() {
    // TODO: 实现联系客服功能
    print('联系客服确认订单');
  }

  @override
  Future<void> close() {
    _countdownTimer?.cancel();
    return super.close();
  }
}
