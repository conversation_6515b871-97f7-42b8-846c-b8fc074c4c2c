import 'package:equatable/equatable.dart';

class UsdtDepositInProcessState extends Equatable {
  final String? orderNumber;
  final String? rechargeAmount;
  final String? depositQuantity;
  final String? exchangeRate;
  final String? depositAddress;
  final String? rechargeNetwork;
  final String? orderStatus;
  final int? countdownSeconds;

  const UsdtDepositInProcessState({
    this.orderNumber,
    this.rechargeAmount,
    this.depositQuantity,
    this.exchangeRate,
    this.depositAddress,
    this.rechargeNetwork,
    this.orderStatus,
    this.countdownSeconds,
  });

  UsdtDepositInProcessState copyWith({
    String? orderNumber,
    String? rechargeAmount,
    String? depositQuantity,
    String? exchangeRate,
    String? depositAddress,
    String? rechargeNetwork,
    String? orderStatus,
    int? countdownSeconds,
  }) {
    return UsdtDepositInProcessState(
      orderNumber: orderNumber ?? this.orderNumber,
      rechargeAmount: rechargeAmount ?? this.rechargeAmount,
      depositQuantity: depositQuantity ?? this.depositQuantity,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      depositAddress: depositAddress ?? this.depositAddress,
      rechargeNetwork: rechargeNetwork ?? this.rechargeNetwork,
      orderStatus: orderStatus ?? this.orderStatus,
      countdownSeconds: countdownSeconds ?? this.countdownSeconds,
    );
  }

  @override
  List<Object?> get props => [
        orderNumber,
        rechargeAmount,
        depositQuantity,
        exchangeRate,
        depositAddress,
        rechargeNetwork,
        orderStatus,
        countdownSeconds,
      ];
}
