import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/models/apis/deposit.dart';

import 'usdt_deposit_state.dart';

class UsdtDepositCubit extends Cubit<UsdtDepositState> {
  UsdtDepositCubit() : super(const UsdtDepositState()) {
    fetchUsdtList();
  }

  Future<void> fetchUsdtList() async {
    final result = await DepositApi.fetchUsdtList();
    emit(state.copyWith(usdtList: result));
  }

  void selectChannel(int channelId) {
    final selectedChannel = state.usdtList
        .where(
          (channel) => channel.id == channelId,
        )
        .firstOrNull;
    if (selectedChannel != null) {
      emit(state.copyWith(selectedChannel: selectedChannel));
    }
  }

  void updateAmount(String amount) {
    emit(state.copyWith(selectedAmount: amount));
  }

  void clearForm() {
    emit(state.copyWith(selectedChannel: null, selectedAmount: null));
  }
}
