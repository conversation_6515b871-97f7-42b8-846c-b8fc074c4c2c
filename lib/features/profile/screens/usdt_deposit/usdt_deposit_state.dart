import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';

class UsdtDepositState extends Equatable {
  final List<USDTChannel> usdtList;
  final USDTChannel? selectedChannel;
  final String? selectedAmount;

  const UsdtDepositState({
    this.usdtList = const [],
    this.selectedChannel,
    this.selectedAmount,
  });

  UsdtDepositState copyWith({
    List<USDTChannel>? usdtList,
    USDTChannel? selectedChannel,
    String? selectedAmount,
  }) {
    return UsdtDepositState(
      usdtList: usdtList ?? this.usdtList,
      selectedChannel: selectedChannel ?? this.selectedChannel,
      selectedAmount: selectedAmount ?? this.selectedAmount,
    );
  }

  @override
  List<Object?> get props => [
        usdtList,
        selectedChannel,
        selectedAmount,
      ];
}
