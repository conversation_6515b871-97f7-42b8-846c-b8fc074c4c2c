import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:collection/collection.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';

import 'usdt_deposit_cubit.dart';
import 'usdt_deposit_state.dart';

class UsdtDepositPage extends StatefulWidget {
  @override
  State<UsdtDepositPage> createState() => _UsdtDepositPageState();
}

class _UsdtDepositPageState extends State<UsdtDepositPage> {
  final TextEditingController _amountController = TextEditingController();
  USDTChannel? _selectedChannel;
  String? _selectedAmount;
  bool _isAmountValid = true;

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_onAmountChanged);
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    setState(() {
      _isAmountValid = _validateAmount(_amountController.text);
    });
  }

  bool _validateAmount(String value) {
    if (value.isEmpty) return true;
    try {
      final amount = double.parse(value);
      final min = _selectedChannel?.minAmount ?? 0;
      final max = _selectedChannel?.maxAmount ?? double.infinity;
      return amount >= min && amount <= max;
    } catch (_) {
      return false;
    }
  }

  void _onAmountButtonPressed(String amount) {
    setState(() {
      _selectedAmount = amount;
      _amountController.text = amount;
    });
  }

  void _onChannelChanged(USDTChannel? channel) {
    setState(() {
      _selectedChannel = channel;
      _isAmountValid = _validateAmount(_amountController.text);
    });
  }

  String _getNetworkName(int network) {
    switch (network) {
      case 1:
        return 'TRC20';
      case 2:
        return 'ERC20';
      default:
        return 'TRC20';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UsdtDepositCubit, UsdtDepositState>(
      builder: (context, state) {
        return Scaffold(
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  16.verticalSpace,
                  _buildChannelSection(state),
                  10.verticalSpace,
                  _buildAmountSection(state),

                  100.verticalSpace, // 为底部按钮留出空间
                ],
              ),
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(16.gw),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildSubmitButton(),
                16.verticalSpace,
                SupportWidget(),
                16.verticalSpace,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChannelSection(UsdtDepositState state) {
    final channels = state.usdtList;
    if (channels.isEmpty) {
      return ShadowBox(
        borderRadius: BorderRadius.circular(8.gr),
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(20.gw),
            child: Text(
              'no_available_recharge_channels'.tr(), // 暂无可用充值渠道
              style: context.textTheme.regular,
            ),
          ),
        ),
      );
    }

    // 如果没有选中的渠道，默认选择第一个
    if (_selectedChannel == null && channels.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _onChannelChanged(channels.first);
      });
    }

    return ShadowBox(
      borderRadius: BorderRadius.circular(8.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'rechargeNetwork'.tr(), // 充值网络
                style: context.textTheme.regular.fs16.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
              Text(
                'singleTransactionLimit'.tr(args: [
                  _selectedChannel?.minAmount.toStringAsFixed(0) ?? '1',
                  _selectedChannel?.maxAmount.toStringAsFixed(0) ?? '999999999'
                ]), // 单笔限额:1-999999999
                style: context.textTheme.regular.fs12.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
            ],
          ),
          16.verticalSpace,
          CommonDropdown<USDTChannel>(
            dropDownValue: channels
                .map((channel) => DropDownValue(
                      id: channel.id.toString(),
                      value: _getNetworkName(channel.network),
                    ))
                .toList(),
            selectedItem: _selectedChannel == null
                ? null
                : DropDownValue(
                    id: _selectedChannel!.id.toString(),
                    value: _getNetworkName(_selectedChannel!.network),
                  ),
            hintText: 'pleaseSelectRechargeNetwork'.tr(),
            // 请选择充值网络
            showSearchBox: false,
            onChanged: (value) {
              final selectedChannel = channels
                  .where(
                    (e) => e.id.toString() == value?.id,
                  )
                  .firstOrNull;
              _onChannelChanged(selectedChannel);
            },
            itemBuilder: (context, item, isDisabled, isSelected) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 16.gh),
                child: Text(item.value ?? ''),
              );
            },
          ),
          10.verticalSpace,
          Text(
            'differentProtocolWalletAddressesNotInteroperable'.tr(), // *不同协议钱包地址不互通,操作请务必鉴别,转账应严格注意充值对应地址
            style: context.textTheme.regular.fs12.copyWith(
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection(UsdtDepositState state) {
    if (_selectedChannel == null) return SizedBox.shrink();

    final amountOptions = _selectedChannel!.rechargeAmountOptionsList;
    final predefinedAmounts = amountOptions.isNotEmpty ? amountOptions : ['100', '200', '500', '1000', '2000', '5000'];

    return ShadowBox(
      borderRadius: BorderRadius.circular(8.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'depositAmount'.tr(), // 充值金额
                style: context.textTheme.title,
              ),
              Text(
                'currentExchangeRate'.tr(args: [_selectedChannel!.exchangeRate.toStringAsFixed(2)]), // 当前汇率:1:7.16
                style: context.textTheme.regular.fs12.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
            ],
          ),
          16.verticalSpace,
          Wrap(
            spacing: 10.gw,
            runSpacing: 10.gh,
            children: predefinedAmounts.map((amount) {
              final isSelected = _selectedAmount == amount;
              return GestureDetector(
                onTap: () => _onAmountButtonPressed(amount),
                child: Container(
                  width: (MediaQuery.of(context).size.width - 32.gw - 20.gw) / 3,
                  height: 40.gh,
                  decoration: BoxDecoration(
                    color: isSelected ? context.theme.primaryColor : Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.gr),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    amount,
                    style: context.textTheme.regular.fs13.copyWith(
                      color: isSelected ? Colors.white : context.colorTheme.textPrimary,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          16.verticalSpace,
          TextFieldWidget(
            controller: _amountController,
            hintText: 'selectAmountOrEnterAmount'.tr(), // 选定金额或输入金额
            textInputType: TextInputType.number,
            errorText: !_isAmountValid ? 'amountExceedsLimitRange'.tr() : null, // 金额超出限额范围
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    final isValid = _selectedChannel != null && _amountController.text.isNotEmpty && _isAmountValid;

    return SizedBox(
      width: double.infinity,
      child: CommonButton(
        onPressed: isValid
            ? () {
                // TODO: 实现提交逻辑
                print('提交充值: ${_amountController.text} USDT');
              }
            : null,
        title: 'submit'.tr(), // 提交
        enable: isValid,
      ),
    );
  }
}
