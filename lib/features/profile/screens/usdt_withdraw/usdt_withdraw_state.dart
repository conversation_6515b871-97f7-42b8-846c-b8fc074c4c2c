import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

class UsdtWithdrawState extends Equatable {
  final List<USDTChannel> usdtChannels;
  final USDTChannel? selectedChannel;
  final bool isLoadingChannels;
  final String? selectedAmount;
  final String? rechargeAmount;
  final List<USDTWallet> usdtWalletAddresses;
  final bool isLoadingWalletAddresses;
  final USDTWallet? selectedWalletAddress;
  final bool isSubmitting;
  final bool isSubmitSuccess;

  const UsdtWithdrawState({
    this.usdtChannels = const [],
    this.selectedChannel,
    this.isLoadingChannels = false,
    this.selectedAmount,
    this.rechargeAmount,
    this.usdtWalletAddresses = const [],
    this.isLoadingWalletAddresses = false,
    this.selectedWalletAddress,
    this.isSubmitting = false,
    this.isSubmitSuccess = false,
  });

  UsdtWithdrawState copyWith({
    List<USDTChannel>? usdtChannels,
    USDTChannel? selectedChannel,
    bool? isLoadingChannels,
    String? selectedAmount,
    String? rechargeAmount,
    List<USDTWallet>? usdtWalletAddresses,
    bool? isLoadingWalletAddresses,
    USDTWallet? selectedWalletAddress,
    bool? isSubmitting,
    bool? isSubmitSuccess,
  }) {
    return UsdtWithdrawState(
      usdtChannels: usdtChannels ?? this.usdtChannels,
      selectedChannel: selectedChannel ?? this.selectedChannel,
      isLoadingChannels: isLoadingChannels ?? this.isLoadingChannels,
      selectedAmount: selectedAmount ?? this.selectedAmount,
      rechargeAmount: rechargeAmount ?? this.rechargeAmount,
      usdtWalletAddresses: usdtWalletAddresses ?? this.usdtWalletAddresses,
      isLoadingWalletAddresses: isLoadingWalletAddresses ?? this.isLoadingWalletAddresses,
      selectedWalletAddress: selectedWalletAddress ?? this.selectedWalletAddress,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isSubmitSuccess: isSubmitSuccess ?? this.isSubmitSuccess,
    );
  }

  @override
  List<Object?> get props => [
        usdtChannels,
        selectedChannel,
        isLoadingChannels,
        selectedAmount,
        rechargeAmount,
        usdtWalletAddresses,
        isLoadingWalletAddresses,
        selectedWalletAddress,
        isSubmitting,
        isSubmitSuccess,
      ];
}
