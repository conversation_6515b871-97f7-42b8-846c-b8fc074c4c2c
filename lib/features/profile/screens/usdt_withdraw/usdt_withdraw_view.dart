import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/counter_textfield.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';

import 'usdt_withdraw_cubit.dart';
import 'usdt_withdraw_state.dart';
import 'widgets/usdt_withdraw_password_dialog.dart';
import 'widgets/wallet_address_selection_bottom_sheet.dart';

class UsdtWithdrawView extends StatefulWidget {
  const UsdtWithdrawView({super.key});

  @override
  State<UsdtWithdrawView> createState() => _UsdtWithdrawViewState();
}

class _UsdtWithdrawViewState extends State<UsdtWithdrawView> {
  final TextEditingController _amountController = TextEditingController();

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    context.read<UsdtWithdrawCubit>().stream.listen((state) {
      if (state.rechargeAmount != null && _amountController.text != state.rechargeAmount) {
        _amountController.text = state.rechargeAmount!;
      } else if (state.rechargeAmount == null && _amountController.text.isNotEmpty) {
        _amountController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<UsdtWithdrawCubit>(context);
    return BlocListener<UsdtWithdrawCubit, UsdtWithdrawState>(
      listener: (context, state) {
        if (state.isSubmitSuccess) {
          cubit.clearForm();
          _amountController.clear();
          GPEasyLoading.showToast('withdrawalSuccessful'.tr());
        } 
      },
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16,
              children: [
                _buildChannelDropdown(context, cubit),
                _buildWalletAddressSelection(context, cubit),
                _buildRechargeAmountSection(context, cubit),
                SizedBox(height: 100.gh), // Space for bottom navigation bar
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: EdgeInsets.all(16.gw),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSubmitButton(context, cubit),
              SizedBox(height: 16.gh),
              SupportWidget(),
              SizedBox(height: 16.gh),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChannelDropdown(BuildContext context, UsdtWithdrawCubit cubit) {
    return ShadowBox(
      child: BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
        builder: (context, state) {
          if (state.isLoadingChannels) {
            return _buildChannelDropdownShimmer();
          }

          final dropdownItems = _convertChannelsToDropdownValues(state.usdtChannels);
          final selectedItem =
              state.selectedChannel != null ? _convertChannelToDropdownValue(state.selectedChannel!) : null;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'withdrawChannel'.tr(),
                style: context.textTheme.regular.copyWith(
                  fontSize: 14.gsp,
                  fontWeight: FontWeight.w500,
                  color: context.colorTheme.textTitle,
                ),
              ),
              SizedBox(height: 8.gh),
              CommonDropdown<DropDownValue>(
                hintText: 'pleaseSelectRechargeNetwork'.tr(),
                dropDownValue: dropdownItems,
                selectedItem: selectedItem,
                onChanged: (DropDownValue? value) {
                  if (value?.code != null) {
                    cubit.selectChannel(value!.code as int);
                  }
                },
                showSearchBox: false,
                height: 48.gh,
                borderRadius: 8.gr,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildChannelDropdownShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerWidget(
          height: 16.gh,
          width: 120.gw,
          radius: 4.gr,
        ),
        SizedBox(height: 8.gh),
        ShimmerWidget(
          height: 48.gh,
          width: double.infinity,
          radius: 8.gr,
        ),
      ],
    );
  }

  Widget _buildWalletAddressSelection(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        if (state.selectedChannel == null) {
          return const SizedBox.shrink();
        }

        return ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'withdrawalAddress'.tr(),
                style: context.textTheme.regular.copyWith(
                  fontSize: 14.gsp,
                  fontWeight: FontWeight.w500,
                  color: context.colorTheme.textTitle,
                ),
              ),
              SizedBox(height: 8.gh),
              GestureDetector(
                onTap: () {
                  _showWalletAddressBottomSheet(context, cubit, state);
                },
                child: Container(
                  width: double.infinity,
                  height: 77.gh,
                  decoration: BoxDecoration(
                    color: context.theme.inputDecorationTheme.fillColor,
                    borderRadius: BorderRadius.circular(8.gr),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 16.gw,
                          height: 16.gh,
                          decoration: BoxDecoration(
                            color: context.theme.inputDecorationTheme.fillColor,
                            borderRadius: BorderRadius.circular(2.gr),
                          ),
                          child: Icon(
                            Icons.add,
                            size: 12.gsp,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 13.gw),
                        Text(
                          state.selectedWalletAddress != null
                              ? state.selectedWalletAddress!.walletAddress
                              : 'addWithdrawalAddress'.tr(),
                          style: context.textTheme.regular.copyWith(
                            fontSize: 14.gsp,
                            fontWeight: FontWeight.w500,
                            color:
                                state.selectedWalletAddress != null ? const Color(0xFF2D2D2D) : const Color(0xFFAFB8CB),
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showWalletAddressBottomSheet(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    if (state.selectedChannel == null) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => WalletAddressSelectionBottomSheet(
        walletAddresses: state.usdtWalletAddresses,
        selectedWallet: state.selectedWalletAddress,
        selectedNetwork: state.selectedChannel!.network,
        onWalletSelected: (wallet) {
          cubit.selectWalletAddress(wallet);
        },
      ),
    ).then((result) {
      // If result is 'refresh', refresh the wallet list
      if (result == 'refresh') {
        cubit.fetchUsdtWalletAddressList();
      }
    });
  }

  List<DropDownValue> _convertChannelsToDropdownValues(List<USDTChannel> channels) {
    return channels.map((channel) => _convertChannelToDropdownValue(channel)).toList();
  }

  DropDownValue _convertChannelToDropdownValue(USDTChannel channel) {
    final networkName = _getNetworkName(channel.network);
    return DropDownValue(
      id: channel.id.toString(),
      value: '${channel.currency} ($networkName)',
      code: channel.id,
    );
  }

  String _getNetworkName(int network) {
    switch (network) {
      case 1:
        return 'TRC20';
      case 2:
        return 'ERC20';
      case 3:
        return 'BEP20';
      default:
        return 'Unknown';
    }
  }

  Widget _buildRechargeAmountSection(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        if (state.selectedChannel == null) {
          return const SizedBox.shrink();
        }

        return ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRechargeAmountHeader(context, state),
              _buildAmountOptionsGrid(context, cubit, state),
              SizedBox(height: 16.gh),
              _buildAmountTextField(context, cubit, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRechargeAmountHeader(BuildContext context, UsdtWithdrawState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'rechargeAmount'.tr(),
          style: context.textTheme.regular.copyWith(
            fontSize: 14.gsp,
            fontWeight: FontWeight.w500,
            color: context.colorTheme.textTitle,
          ),
        ),
        Text(
          '当前汇率:\n1: ${state.selectedChannel?.exchangeRate.toStringAsFixed(2) ?? '7.16'}',
          textAlign: TextAlign.right,
          style: context.textTheme.regular.copyWith(
            fontSize: 14.gsp,
            color: context.colorTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAmountOptionsGrid(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    final options = state.selectedChannel?.rechargeAmountOptionsList ?? [];

    if (options.isEmpty) {
      return const SizedBox.shrink();
    }

    return Wrap(
      spacing: 10.gw,
      runSpacing: 12.gh,
      children: options.map((amount) => _buildAmountOptionButton(context, cubit, state, amount)).toList(),
    );
  }

  Widget _buildAmountOptionButton(
      BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state, String amount) {
    final isSelected = state.rechargeAmount == amount;

    return GestureDetector(
      onTap: () {
        cubit.updateRechargeAmount(amount);
        _amountController.text = amount;
      },
      child: Container(
        width: (1.gsw / 4) - 22.gw,
        height: 28.gh,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3252BF) : const Color(0xFFF1F4FF),
          borderRadius: BorderRadius.circular(6.gr),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF354777).withValues(alpha: 0.02),
              offset: const Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: Center(
          child: Text(
            amount,
            style: context.textTheme.regular.copyWith(
              fontSize: 14.gsp,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : const Color(0xFF2D2D2D),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAmountTextField(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    return Container(
      height: 42.gh,
      decoration: BoxDecoration(
        color: const Color(0xFFF1F4FF),
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: TextField(
        controller: _amountController,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[\d\.,]')),
          CommaReplacementFormatter(),
        ],
        onChanged: (value) {
          cubit.updateRechargeAmount(value);
        },
        decoration: InputDecoration(
          hintText: 'enterRechargeAmount'.tr(),
          hintStyle: context.textTheme.regular.copyWith(
            fontSize: 14.gsp,
            color: const Color(0xFFAFB8CB),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 11.gh),
        ),
        style: context.textTheme.regular.copyWith(
          fontSize: 14.gsp,
          color: const Color(0xFF2D2D2D),
        ),
      ),
    );
  }

  Widget _buildSubmitButton(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        final isEnabled = _canSubmit(state);

        return CustomMaterialButton(
          isEnabled: isEnabled,
          onPressed: isEnabled ? () => _handleSubmit(context, cubit, state) : null,
          buttonText: 'submit'.tr(),
          color: context.theme.primaryColor,
          borderColor: context.theme.primaryColor,
          borderRadius: 5.gr,
          textColor: Colors.white,
          fontSize: 13.gr,
        );
      },
    );
  }

  bool _canSubmit(UsdtWithdrawState state) {
    return state.selectedChannel != null &&
        state.selectedWalletAddress != null &&
        state.rechargeAmount != null &&
        state.rechargeAmount!.isNotEmpty &&
        !state.isLoadingChannels &&
        !state.isLoadingWalletAddresses &&
        !state.isSubmitting;
  }

  void _handleSubmit(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    // Validate all required fields
    if (state.selectedChannel == null) {
      // Show error for missing channel
      return;
    }

    if (state.selectedWalletAddress == null) {
      // Show error for missing wallet address
      return;
    }

    if (state.rechargeAmount == null || state.rechargeAmount!.isEmpty) {
      // Show error for missing amount
      return;
    }

    // Show password dialog
    _showPasswordDialog(context, cubit);
  }

  void _showPasswordDialog(BuildContext context, UsdtWithdrawCubit cubit) {
    showDialog(
      context: context,
      builder: (context) => UsdtWithdrawPasswordDialog(
        onSubmit: (password) {
          cubit.submitWithdraw(password: password);
        },
      ),
    );
  }
}
