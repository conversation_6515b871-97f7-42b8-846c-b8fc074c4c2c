import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import 'usdt_wallet_manage_cubit.dart';
import 'usdt_wallet_manage_state.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/common_dialog.dart';

class UsdtWalletManagePage extends StatelessWidget {
  const UsdtWalletManagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => UsdtWalletManageCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<UsdtWalletManageCubit>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'usdtAddressManagement'.tr(), // USDT地址管理
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 主要内容区域
            Expanded(
              child: BlocBuilder<UsdtWalletManageCubit, UsdtWalletManageState>(
                builder: (context, state) {
                  if (state.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state.error != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48.gw,
                            color: context.colorTheme.stockRed,
                          ),
                          SizedBox(height: 16.gw),
                          Text(
                            'loadFailed'.tr(), // 加载失败
                            style: context.textTheme.primary.fs16.w500,
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 8.gw),
                          Text(
                            state.error!,
                            style: context.textTheme.regular,
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 16.gw),
                          ElevatedButton(
                            onPressed: () => cubit.fetchWalletList(),
                            child: Text('retry'.tr()), // 重试
                          ),
                        ],
                      ),
                    );
                  }

                  if (state.walletList.isEmpty) {
                    return _buildEmptyState(context);
                  }

                  return _buildWalletList(context, state, cubit);
                },
              ),
            ),
          ],
        ),
      ),

      // 底部添加地址按钮
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(
          left: 16.gw,
          right: 16.gw,
          bottom: 80.gw, // 固定在底部80.gw位置
          top: 16.gw,
        ),
        child: ElevatedButton(
          onPressed: () async {
            await getIt<NavigatorService>().push(AppRouter.routeUSDTWalletEdit);
            cubit.fetchWalletList();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: context.colorTheme.textHighlight,
            foregroundColor: context.colorTheme.textSecondary,
            minimumSize: Size(double.infinity, 48.gw),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.gw),
            ),
          ),
          child: Text(
            'addAddress'.tr(), // 添加地址
            style: context.textTheme.primary.fs16.w600.copyWith(
              color: context.colorTheme.textSecondary,
            ),
          ),
        ),
      ),
    );
  }

  /// 空状态界面
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 空状态图标
          Image.asset(
            'assets/images/icon_usdt_wallet_empty.png',
            width: 120.gw,
            height: 120.gw,
          ),

          SizedBox(height: 24.gw),

          // 占位符线条
          Container(
            width: 60.gw,
            height: 4.gw,
            decoration: BoxDecoration(
              color: context.colorTheme.border,
              borderRadius: BorderRadius.circular(2.gw),
            ),
          ),

          SizedBox(height: 8.gw),

          Container(
            width: 40.gw,
            height: 4.gw,
            decoration: BoxDecoration(
              color: context.colorTheme.border,
              borderRadius: BorderRadius.circular(2.gw),
            ),
          ),

          SizedBox(height: 24.gw),

          // 提示文字
          Text(
            'noUsdtAddressAdded'.tr(), // 未添加USDT地址
            style: context.textTheme.regular.fs16.copyWith(
              color: context.colorTheme.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  /// 钱包列表界面
  Widget _buildWalletList(
    BuildContext context,
    UsdtWalletManageState state,
    UsdtWalletManageCubit cubit,
  ) {
    return ListView.builder(
      padding: EdgeInsets.all(16.gw),
      itemCount: state.walletList.length,
      itemBuilder: (context, index) {
        final wallet = state.walletList[index];
        return _buildWalletItem(context, wallet, cubit);
      },
    );
  }

  /// 单个钱包项
  Widget _buildWalletItem(
    BuildContext context,
    USDTWallet wallet,
    UsdtWalletManageCubit cubit,
  ) {
    final isDefault = wallet.isWithdrawDefault == 1;

    return Container(
      margin: EdgeInsets.only(bottom: 12.gw),
      padding: EdgeInsets.all(16.gw),
      decoration: BoxDecoration(
        color: context.colorTheme.textSecondary,
        borderRadius: BorderRadius.circular(12.gw),
        border: Border.all(
          color: context.colorTheme.border,
          width: 1.gw,
        ),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 8.gw,
            offset: Offset(0, 2.gw),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部行：网络类型和删除按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 网络类型标签
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
                decoration: BoxDecoration(
                  color: context.colorTheme.textHighlight.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.gw),
                ),
                child: Text(
                  cubit.getNetworkDisplayName(wallet.network),
                  style: context.textTheme.primary.fs12.w600.copyWith(
                    color: context.colorTheme.textHighlight,
                  ),
                ),
              ),

              // 编辑和删除按钮
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 编辑按钮
                  GestureDetector(
                    onTap: () async {
                      await getIt<NavigatorService>().push(AppRouter.routeUSDTWalletEdit, arguments: wallet);
                      cubit.fetchWalletList();
                    },
                    child: Container(
                      padding: EdgeInsets.all(8.gw),
                      child: Icon(
                        Icons.edit_outlined,
                        size: 20.gw,
                        color: context.colorTheme.textHighlight,
                      ),
                    ),
                  ),

                  SizedBox(width: 8.gw),

                  // 删除按钮
                  GestureDetector(
                    onTap: () => _showDeleteConfirmDialog(context, wallet, cubit),
                    child: Container(
                      padding: EdgeInsets.all(8.gw),
                      child: Icon(
                        Icons.delete_outline,
                        size: 20.gw,
                        color: context.colorTheme.stockRed,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 12.gw),

          // 钱包地址
          Text(
            wallet.walletAddress,
            style: context.textTheme.regular.fs16.copyWith(
              color: context.colorTheme.textTitle,
              fontFamily: 'monospace',
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          SizedBox(height: 16.gw),

          // 底部行：设置默认和默认状态
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 设置默认按钮
              GestureDetector(
                onTap: isDefault ? null : () => cubit.setDefaultWallet(wallet.id),
                child: Text(
                  'setAsDefault'.tr(), // 设为默认
                  style: context.textTheme.primary.fs16.w500.copyWith(
                    color: isDefault ? context.colorTheme.textTertiary : context.colorTheme.textHighlight,
                  ),
                ),
              ),

              // 默认状态指示器
              Container(
                width: 20.gw,
                height: 20.gw,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: context.colorTheme.textHighlight,
                    width: 2.gw,
                  ),
                  color: isDefault ? context.colorTheme.textHighlight : Colors.transparent,
                ),
                child: isDefault
                    ? Icon(
                        Icons.check,
                        size: 12.gw,
                        color: context.colorTheme.textSecondary,
                      )
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(
    BuildContext context,
    USDTWallet wallet,
    UsdtWalletManageCubit cubit,
  ) {
    CommonDialog(
      context,
      title: 'confirmDelete'.tr(), // 确认删除
      content: 'confirmDeleteUsdtAddress'.tr(), // 确定要删除这个USDT地址吗？删除后无法恢复。
      sureBtnTitle: 'delete'.tr(), // 删除
      sureBtnTitleStyle: context.textTheme.primary.copyWith(
        color: context.colorTheme.stockRed,
      ),
      complete: () => cubit.deleteWallet(wallet.id),
    ).show();
  }
}
