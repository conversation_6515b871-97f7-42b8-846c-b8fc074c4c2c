import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

class UsdtWalletEditState extends Equatable {
  final USDTWallet? wallet; // 如果为null则为新增，否则为编辑
  final String walletAddress;
  final int selectedNetwork;
  final bool isDefaultAddress;
  final bool isAdding;
  final bool isEditing;
  final String? error;

  UsdtWalletEditState({
    this.wallet,
    this.walletAddress = '',
    this.selectedNetwork = 1, // 默认选择TRC20
    this.isDefaultAddress = false,
    this.isAdding = false,
    this.isEditing = false,
    this.error,
  });

  @override
  List<Object?> get props => [
        wallet,
        walletAddress,
        selectedNetwork,
        isDefaultAddress,
        isAdding,
        isEditing,
        error,
      ];

  UsdtWalletEditState copyWith({
    USDTWallet? wallet,
    String? walletAddress,
    int? selectedNetwork,
    bool? isDefaultAddress,
    bool? isAdding,
    bool? isEditing,
    String? error,
  }) {
    return UsdtWalletEditState(
      wallet: wallet ?? this.wallet,
      walletAddress: walletAddress ?? this.walletAddress,
      selectedNetwork: selectedNetwork ?? this.selectedNetwork,
      isDefaultAddress: isDefaultAddress ?? this.isDefaultAddress,
      isAdding: isAdding ?? this.isAdding,
      isEditing: isEditing ?? this.isEditing,
      error: error ?? this.error,
    );
  }
}
