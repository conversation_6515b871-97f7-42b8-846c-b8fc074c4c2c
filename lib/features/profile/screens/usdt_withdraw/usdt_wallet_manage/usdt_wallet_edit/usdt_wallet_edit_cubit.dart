import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/models/apis/wallet.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

import 'usdt_wallet_edit_state.dart';

class UsdtWalletEditCubit extends Cubit<UsdtWalletEditState> {
  UsdtWalletEditCubit({USDTWallet? wallet}) : super(UsdtWalletEditState(wallet: wallet)) {
    if (wallet != null) {
      setEditMode(wallet);
    }
  }

  /// 新增钱包地址
  Future<void> addWalletAddress() async {
    if (state.walletAddress.trim().isEmpty) {
      GPEasyLoading.showToast('pleaseEnterWalletAddress'.tr()); // 请输入钱包地址
      return;
    }

    emit(state.copyWith(isAdding: true));

    try {
      final success = await WalletApi.addUsdtWalletAddress(
        walletAddress: state.walletAddress.trim(),
        network: state.selectedNetwork,
        isWithdrawDefault: state.isDefaultAddress,
      );

      if (success) {
        GPEasyLoading.showToast('addSuccess'.tr()); // 添加成功
        // 重置表单
        emit(state.copyWith(
          walletAddress: '',
          selectedNetwork: 1,
          isDefaultAddress: false,
        ));
      } else {
        GPEasyLoading.showToast('addFailed'.tr()); // 添加失败
      }
    } catch (e) {
      GPEasyLoading.showToast('addFailedWithError'.tr(args: [e.toString()])); // 添加失败: {}
    } finally {
      emit(state.copyWith(isAdding: false));
    }
  }

  /// 编辑钱包地址
  Future<void> editWalletAddress() async {
    if (state.wallet == null) {
      GPEasyLoading.showToast('walletNotFound'.tr()); // 钱包不存在
      return;
    }

    if (state.walletAddress.trim().isEmpty) {
      GPEasyLoading.showToast('pleaseEnterWalletAddress'.tr()); // 请输入钱包地址
      return;
    }

    emit(state.copyWith(isEditing: true));

    try {
      final success = await WalletApi.editUsdtWalletAddress(
        id: state.wallet!.id,
        walletAddress: state.walletAddress.trim(),
        network: state.selectedNetwork,
        isWithdrawDefault: state.isDefaultAddress,
      );

      if (success) {
        GPEasyLoading.showToast('editSuccess'.tr()); // 编辑成功
        // 重置表单
        emit(state.copyWith(
          walletAddress: '',
          selectedNetwork: 1,
          isDefaultAddress: false,
        ));
      } else {
        GPEasyLoading.showToast('editFailed'.tr()); // 编辑失败
      }
    } catch (e) {
      GPEasyLoading.showToast('editFailedWithError'.tr(args: [e.toString()])); // 编辑失败: {}
    } finally {
      emit(state.copyWith(isEditing: false));
    }
  }

  /// 更新钱包地址输入
  void updateWalletAddress(String address) {
    emit(state.copyWith(walletAddress: address));
  }

  /// 更新选择的网络
  void updateSelectedNetwork(int network) {
    emit(state.copyWith(selectedNetwork: network));
  }

  /// 更新是否设为默认地址
  void updateIsDefaultAddress(bool isDefault) {
    emit(state.copyWith(isDefaultAddress: isDefault));
  }

  /// 设置编辑模式（预填充表单数据）
  void setEditMode(USDTWallet wallet) {
    emit(state.copyWith(
      wallet: wallet,
      walletAddress: wallet.walletAddress,
      selectedNetwork: wallet.network,
      isDefaultAddress: wallet.isWithdrawDefault == 1,
    ));
  }

  /// 重置表单
  void resetForm() {
    emit(state.copyWith(
      walletAddress: '',
      selectedNetwork: 1,
      isDefaultAddress: false,
    ));
  }

  /// 获取网络类型显示名称
  String getNetworkDisplayName(int network) {
    switch (network) {
      case 1:
        return 'TRC20';
      case 2:
        return 'ERC20';
      case 3:
        return 'BEP20';
      default:
        return 'Unknown';
    }
  }

  /// 获取网络选项列表
  List<Map<String, dynamic>> getNetworkOptions() {
    return [
      {'id': 1, 'name': 'TRC20'},
      {'id': 2, 'name': 'ERC20'},
      {'id': 3, 'name': 'BEP20'},
    ];
  }
}
