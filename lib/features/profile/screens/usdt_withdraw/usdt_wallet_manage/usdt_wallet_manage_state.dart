import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

class UsdtWalletManageState extends Equatable {
  final List<USDTWallet> walletList;
  final bool isLoading;
  final String? error;
  final bool isDeleting;
  final bool isSettingDefault;

  UsdtWalletManageState({
    this.walletList = const [],
    this.isLoading = false,
    this.error,
    this.isDeleting = false,
    this.isSettingDefault = false,
  });

  @override
  List<Object?> get props => [
        walletList,
        isLoading,
        error,
        isDeleting,
        isSettingDefault,
      ];

  UsdtWalletManageState copyWith({
    List<USDTWallet>? walletList,
    bool? isLoading,
    String? error,
    bool? isDeleting,
    bool? isSettingDefault,
  }) {
    return UsdtWalletManageState(
      walletList: walletList ?? this.walletList,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isDeleting: isDeleting ?? this.isDeleting,
      isSettingDefault: isSettingDefault ?? this.isSettingDefault,
    );
  }
}
