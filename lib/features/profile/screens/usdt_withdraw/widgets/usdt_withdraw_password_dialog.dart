import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/custom_pin_keyboard.dart';
import 'package:pinput/pinput.dart';
import 'package:gp_stock_app/features/main/widgets/draggable_float_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class UsdtWithdrawPasswordDialog extends StatefulWidget {
  final Function(String password) onSubmit;

  const UsdtWithdrawPasswordDialog({
    super.key,
    required this.onSubmit,
  });

  @override
  State<UsdtWithdrawPasswordDialog> createState() => _UsdtWithdrawPasswordDialogState();
}

class _UsdtWithdrawPasswordDialogState extends State<UsdtWithdrawPasswordDialog> {
  final TextEditingController passwordController = TextEditingController();
  late PinTheme defaultPinTheme;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showKeyboard();
    });
  }

  @override
  void dispose() {
    passwordController.dispose();
    super.dispose();
  }

  void _showKeyboard() {
    FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.transparent, // Lighter overlay
      builder: (_) => WithdrawalPasswordKeyboard(
        controller: passwordController,
        bottomPadding: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom,
        onSubmit: () {
          Navigator.pop(context);
          _submitWithdrawal();
        },
      ),
    ).whenComplete(() {
      FloatingWidgetManager().updatePosition(FloatingPosition.bottomRight);
    });
  }

  void _submitWithdrawal() {
    if (passwordController.text.length == 6) {
      Navigator.pop(context); // Close the dialog
      widget.onSubmit(passwordController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    defaultPinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: context.textTheme.primary.w500,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(10.gr),
        border: Border.all(color: context.theme.primaryColor, width: 0.1),
      ),
    );

    return AlertDialog(
      backgroundColor: context.theme.cardColor,
      title: Text('pleaseEnterWithdrawPassword'.tr()),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: _showKeyboard,
            child: AbsorbPointer(
              child: Pinput(
                controller: passwordController,
                length: 6,
                readOnly: true,
                obscureText: true,
                defaultPinTheme: defaultPinTheme,
                focusedPinTheme: defaultPinTheme.copyWith(
                  decoration: defaultPinTheme.decoration!.copyWith(
                    border: Border.all(color: context.theme.primaryColor, width: 0.8),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
